package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pxpat-backend/internal/user-cluster/message-service/config"
	"pxpat-backend/internal/user-cluster/message-service/handler"
	"pxpat-backend/internal/user-cluster/message-service/model"
	"pxpat-backend/internal/user-cluster/message-service/repository/impl"
	"pxpat-backend/internal/user-cluster/message-service/routes"
	"pxpat-backend/internal/user-cluster/message-service/service"
	"pxpat-backend/internal/user-cluster/message-service/websocket"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// 加载配置
	cfg := config.NewConfig()

	// 设置gin模式 - 开发环境下使用debug模式以显示详细信息
	if os.Getenv("GIN_MODE") != "release" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 连接数据库
	db, err := gorm.Open(postgres.Open(cfg.Database.DSN), &gorm.Config{})
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 自动迁移数据表结构
	err = db.AutoMigrate(&model.Message{}, &model.Conversation{})
	if err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 初始化仓库层
	messageRepo := impl.NewMessageRepository(db)
	conversationRepo := impl.NewConversationRepository(db)

	// 初始化服务层
	messageService := service.NewMessageService(messageRepo, conversationRepo)
	conversationService := service.NewConversationService(conversationRepo, messageRepo)

	// 创建WebSocket Hub并启动
	hub := websocket.NewHub()
	go hub.Run()

	// 创建WebSocket处理器
	wsHandler := websocket.NewHandler(hub)

	// 创建HTTP处理器
	messageHandler := handler.NewMessageHandler(messageService)
	conversationHandler := handler.NewConversationHandler(conversationService)

	// 设置WebSocket消息推送接口
	messageService.SetWebSocketHandler(wsHandler)
	messageHandler.SetWebSocketHandler(wsHandler)

	// 创建Gin引擎 - 使用默认中间件
	r := gin.Default()

	// 注册路由
	routes.RegisterRoutes(r, messageHandler, conversationHandler, wsHandler)

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatalf("Consul管理器初始化失败: %v", err)
	}

	// 启动Consul管理器
	consulCtx := context.Background()
	if err := consulManager.Start(consulCtx); err != nil {
		log.Fatalf("Consul管理器启动失败: %v", err)
	}

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    ":" + cfg.Server.Port,
		Handler: r,
	}

	// 启动HTTP服务
	go func() {
		log.Printf("消息服务启动在端口 %s\n", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP服务启动失败: %v", err)
		}
	}()

	// 优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 停止Consul管理器
	if err := consulManager.Stop(); err != nil {
		log.Printf("停止Consul管理器失败: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("服务关闭失败: %v", err)
	}
}
